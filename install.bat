@echo off
echo ===================================================
echo Grand RP Helper - Installation and Protection Setup
echo ===================================================
echo.
echo This script will help you install and protect Grand RP Helper
echo from being removed by Windows updates or antivirus software.
echo.
echo What would you like to do?
echo.
echo 1. Install and protect Grand RP Helper (Recommended)
echo 2. Only add Windows Defender exclusions
echo 3. Exit
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    goto install_and_protect
) else if "%choice%"=="2" (
    goto add_exclusions
) else if "%choice%"=="3" (
    goto end
) else (
    echo Invalid choice. Please try again.
    pause
    exit /b
)

:install_and_protect
echo.
echo Installing Grand RP Helper...
echo.

:: Create a directory in Program Files
set "install_dir=%ProgramFiles%\Grand RP Helper"
if not exist "%install_dir%" mkdir "%install_dir%"

:: Copy all files to the installation directory
echo Copying files to %install_dir%...
xcopy /E /I /Y "." "%install_dir%"

:: Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut([Environment]::GetFolderPath('Desktop') + '\Grand RP Helper.lnk'); $Shortcut.TargetPath = '%install_dir%\Grand_RP_Helper.exe'; $Shortcut.Save()"

:: Add Windows Defender exclusions
echo.
echo Adding Windows Defender exclusions...
cd /d "%install_dir%"
call Add_Windows_Protection.bat

echo.
echo Installation complete!
echo Grand RP Helper has been installed to: %install_dir%
echo A shortcut has been created on your desktop.
echo.
echo You can now run Grand RP Helper from your desktop.
goto end

:add_exclusions
echo.
echo Adding Windows Defender exclusions...
call Add_Windows_Protection.bat
goto end

:end
echo.
echo Thank you for using Grand RP Helper!
echo.
pause
