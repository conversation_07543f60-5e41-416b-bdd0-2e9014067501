# Firebase Configuration Setup

This application uses Firebase for license management and data storage. To set up your own Firebase configuration, follow these steps:

## Option 1: Using Environment Variables

You can set up Firebase credentials using environment variables:

1. Set the `FIREBASE_CONFIG` environment variable with your Firebase service account JSON as a string.
   
   OR
   
2. Set the `FIREBASE_CONFIG_FILE` environment variable to point to your Firebase service account JSON file.

3. Optionally, set the `FIREBASE_DATABASE_URL` environment variable to your Firebase database URL.

## Option 2: Using a Configuration File

1. Create a file named `firebase_config.json` in the same directory as the application.

2. Copy the contents from `firebase_config.json.template` and replace the placeholder values with your actual Firebase credentials.

3. The application will automatically detect and use this file.

## Option 3: Using the Legacy Method

If none of the above methods are set up, the application will look for a file named `serviceAccountKey.json` in the application directory.

## How to Get Firebase Credentials

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select an existing one
3. Go to Project Settings > Service Accounts
4. Click "Generate New Private Key"
5. Save the downloaded JSON file as `firebase_config.json` in the application directory or use one of the other methods described above

## Security Notes

- Keep your Firebase credentials secure and do not share them with others
- When distributing the application, do not include your `firebase_config.json` or `serviceAccountKey.json` files
- Each user should set up their own Firebase configuration using one of the methods described above
