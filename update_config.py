"""
Script to update config.json with encrypted email configuration.
"""

import json
import os
from config_encryption import encrypt_config_section

def update_config():
    """Update config.json with encrypted email configuration."""
    config_file = "config.json"
    
    # Load existing config
    config = {}
    if os.path.exists(config_file):
        try:
            with open(config_file, "r") as file:
                config = json.load(file)
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            print(f"Error loading config file: {str(e)}")
            # Start with empty config if file is corrupted
            config = {}
    
    # Email configuration to encrypt
    email_config = {
        "enabled": True,
        "address": "<EMAIL>",
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "smtp_username": "<EMAIL>",
        "use_tls": True,
        "notify_on_crash": True
    }
    
    # Encrypt email configuration and add to config
    config = encrypt_config_section(config, "email_config", email_config)
    
    # Save updated config
    with open(config_file, "w") as file:
        json.dump(config, file, indent=2)
    
    print(f"Updated {config_file} with encrypted email configuration.")

if __name__ == "__main__":
    update_config()
