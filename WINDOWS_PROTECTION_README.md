# Preventing Windows from Removing Grand RP Helper

This guide explains how to prevent Windows from removing the Grand RP Helper application during Windows updates.

## Why Windows Removes the Application

Windows Defender and other antivirus software may flag the Grand RP Helper application as potentially unwanted because:

1. It uses automation features to control keyboard and mouse
2. It monitors keyboard input for hotkeys
3. It interacts with other applications
4. It connects to online services

These are all legitimate features of the application, but they can trigger false positives in security software.

## Solution 1: Run the Protection Script (Recommended)

The easiest way to prevent Windows from removing the application is to run the included protection script:

1. Locate the file `Add_Windows_Protection.bat` in the application folder
2. Right-click on it and select **Run as administrator**
3. Follow the on-screen instructions
4. When prompted by User Account Control (UAC), click **Yes**

This script will add the application to Windows Defender exclusions, which should prevent it from being removed during updates.

## Solution 2: Manually Add Exclusion to Windows Defender

If the script doesn't work, you can manually add an exclusion:

1. Open **Windows Security** (search for it in the Start menu)
2. Click on **Virus & threat protection**
3. Under "Virus & threat protection settings," click **Manage settings**
4. Scroll down to **Exclusions** and click **Add or remove exclusions**
5. Click **Add an exclusion** and select **File**
6. Browse to the location of `Grand_RP_Helper.exe` and select it
7. Click **Add an exclusion** again and select **Folder**
8. Browse to the folder containing the application and select it

## Solution 3: Disable Real-time Protection Temporarily

If you're having trouble with the above methods, you can temporarily disable real-time protection while installing:

1. Open **Windows Security**
2. Click on **Virus & threat protection**
3. Under "Virus & threat protection settings," click **Manage settings**
4. Turn off **Real-time protection**
5. Install and run the application
6. Turn real-time protection back on
7. Add exclusions as described in Solution 2

**Note:** This is not recommended as a permanent solution, as it leaves your system vulnerable to actual threats.

## Solution 4: Use a Different Antivirus

If Windows Defender continues to remove the application, you might consider using a different antivirus solution that allows more granular control over exclusions.

## Contact Support

If you continue to experience issues with Windows removing the application, please contact support for further assistance.

## Technical Details

The protection script adds the following items to Windows Defender exclusions:
- The Grand_RP_Helper.exe executable file
- The folder containing the application and its configuration files

This ensures that Windows Defender won't flag or remove any part of the application during scans or updates.
