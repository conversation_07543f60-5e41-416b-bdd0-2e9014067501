import os
import json
from firebase_admin import credentials

# Credenciales de Firebase incorporadas directamente en el código
# Esto elimina la necesidad de tener un archivo serviceAccountKey.json
FIREBASE_CREDENTIALS = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

def get_firebase_credentials(is_admin=False):
    """
    Get Firebase credentials from embedded data, environment variables, or a local file.

    Args:
        is_admin (bool): Whether to use admin credentials or user credentials

    The function tries the following methods in order:
    1. Use embedded credentials (default)
    2. Look for environment variables (FIREBASE_CONFIG as JSON string)
    3. Look for a file specified by FIREBASE_CONFIG_FILE environment variable
    4. Look for a file named 'firebase_config.json' in the current directory
    5. If is_admin=False, use 'user_firebase_config.json' (limited access) if available

    Returns:
        credentials.Certificate object
    """
    # Method 0: Use embedded credentials (default)
    try:
        # Usar las credenciales incorporadas directamente
        return credentials.Certificate(FIREBASE_CREDENTIALS)
    except Exception as e:
        print(f"Error using embedded credentials: {e}")
        # Continue with other methods if embedded credentials fail

    # Method 1: Check for FIREBASE_CONFIG environment variable
    firebase_config_json = os.environ.get('FIREBASE_CONFIG')
    if firebase_config_json:
        try:
            config_dict = json.loads(firebase_config_json)
            return credentials.Certificate(config_dict)
        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error parsing FIREBASE_CONFIG environment variable: {e}")

    # Method 2: Check for FIREBASE_CONFIG_FILE environment variable
    config_file_path = os.environ.get('FIREBASE_CONFIG_FILE')
    if config_file_path and os.path.exists(config_file_path):
        try:
            return credentials.Certificate(config_file_path)
        except Exception as e:
            print(f"Error loading Firebase config from {config_file_path}: {e}")

    # Method 3: Look for firebase_config.json in current directory
    if os.path.exists('firebase_config.json'):
        try:
            return credentials.Certificate('firebase_config.json')
        except Exception as e:
            print(f"Error loading Firebase config from firebase_config.json: {e}")

    # Method 4: Use appropriate credentials based on role
    if not is_admin:
        # Regular user gets limited access credentials if available
        if os.path.exists('user_firebase_config.json'):
            return credentials.Certificate('user_firebase_config.json')

    # Method 5: As a last resort, check for serviceAccountKey.json
    if os.path.exists('serviceAccountKey.json'):
        return credentials.Certificate('serviceAccountKey.json')

    # If we get here, we couldn't find any credentials
    raise FileNotFoundError("No Firebase configuration found. Please contact support.")

def get_database_url():
    """Get the Firebase database URL from environment variables or use the default."""
    return os.environ.get('FIREBASE_DATABASE_URL', 'https://autoclicker-7f9ac-default-rtdb.firebaseio.com')
