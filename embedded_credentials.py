"""
Module for storing embedded Firebase credentials.
This approach hides credentials from users by embedding them directly in the code.
"""

import base64
import json
from cryptography.fernet import Fernet

# Encryption key embedded in code (this is just for basic obfuscation)
# In a real-world scenario, you might want to use a more sophisticated approach
_EMBEDDED_KEY = b'5chNjgPX3F5uEZoXM6qsz9ruBmfcnhF5e3unN3suLxI='  # Replace with a real Fernet key

# User credentials encoded and encrypted
# This will be filled with the encrypted content of user_firebase_config.json
_ENCODED_USER_CREDENTIALS = """
gAAAAABoEB5dm1C-7S2-U_eh2GFyjJKUDOX6V90NoiESyLeqsX93-LZMCITnDttEEuS12zm0fDG21LlQUDc4Q7E-BfAy09igRFOtcfD316ZCOEqx8tdTmkbgb72mmRQ7NC6p-CUdDdPlIIkqVCAnb817cGL_Telf_CWpQm6j6ZlznouHywudOjfj-eUQUhaqw8h4frBtGWG1JIoL2XnXCFv0EQn6JCoIikGOBNOH2ArrMUm7gMLIMvNRw_tvdg-6_brQtjS-ybjrfvr2ndjGV3duPJVmGRW_Yxgq8cIvNWby7g2MyGu7jmf_xuXGztXb0o_JfLT1PxSFSELrKYxNB7UZgvY6a4Eui_3eGzQy9Auml_1p4t--TXXIw7EVbStsXpl2EgM9p3-4_bKMz3AlxaQ8vpz0N4Hyj0tpVei-dVcFKkN0dyHeSNUJjmK1PiuBYoAP_ODOM3rpEqXsYOfhLgI-TFtnxMPrCiXAyWEyecktQEkrXkUaY_YrMgCiyh8JLirb0b3L1dasdLfI0d0vKQI-VitmcoDna4Bvg8RKB66eXtR1yAxLdqw8IIoEEI_b6Gn74qT4WFC1CUdWVGo-1J4catU-SfCiGLQyEQg-AzO-UnctaVvWl2BEoIsp8G349FRKExxng0XoMh6SjS4JrYpQ79FS5jSgDiqC7ru9vUHCw_lTPACC_Oq9EUL7SELI7lvSP6FzG_GbGtjrUWSpQzDpa1YQe7_FFol4PIQUZ3mLiBPEJVNz81RLnd4ynMWFJRPTlTzftrpFyUr-f3CYulAxzO7NPe8z0WFNp5RiKeDLeuxFH79JEdk09AiIdM8z-a2gdnjLeqXCjSbwGneA348QQfdnAcH0-z5Ys2K4MqZPTq4Vivm7vz-FEcVGuMgsQmXj-O9HcECZliEF_uNJ_ZhG6wqTDbRnH4vj8I2JLfOa6SnVNgNx0elcNCpVGzHYHve_pMqwy3Cm2uVJA0f3Kt0px0MIsK8263sN-33cTWXcJh3HzKS08-17bbtXkurkNdw4irXpxRpX_grKDkiC-Z-zb0geiOItQ2chKMIG6oNjAFI3KjDwiOXlmqm8v6SdIh8Gu0MUj1lYskF_T6_TAj0mC_u5C0US3wqZRv_DaUG1dDYIEPOok8Q6MYAIvTgen7ZndWtxAvKcI4gvMpciIBUv6fQ9iUHqmqkNEoiUpdezn70KZ675-9VD6EkIXcyfo7o3-hjQ4FhtC5dvw5qG9yovMEAxkTSzlXmqqb3dgg0lQKotiqzfKysB-RVgNdwq64TkgnjBu1fjRhMUADJr0joYs8OVH-hulFJjL65mPkmKXoqCBzG7oZmgtaeFbvS9PBUmQs0Gj6R4_NYew6JmxgbllMQukkUGnsm3N3pBhTCZlhHoG2zuBUUvnKOU6rwchFH7Y7xuO-aWs73WNsL54I1PNeAUMMF6rbwVeucM8hFwXawelWw7-B8UgFtQhB38FguF8OlP7oTJVDh5giSrxixSlEHEY_cKNtoo6h6TQoevQ9k3dp_MrVP4WSz1Gy4reYJ9GximBNDSMv9Sj9VXDjyVBZQWaYboBFvX1gfJGxVuhJc-_zVUEUcI10JaFw6tZJi5N_yniXYP5LKO1mlVKS0VcEheWPVsDGAoMMBQ0Mr8NgsUClRyk53WOK-Sxy1x5TuN7AgrPBxsh-VqDFwUejYlmxgNM8cgA3oIZwq8BgoZe6AOvHjt2Hu1uoCvyelxqU5q_BMDLwzoFBkZDg3scAmKifHOGLVoOPqx4lfsELYqF7S3DGHM8TgDtO_vcwlfsM2v4cAyw284DyfwUsWL5FYaKfMG5SD6pm_4B_H6-3Um2zFdWn6ohrYpjiXvFra-fv8dvavmmWuajeRcR0UNxjZCVCxGINHvJWvtD2XriXPwAblX_R6lQb7UctqdO9X-mQZ2AsN1ORWKW5N75L1nT9ji_rKQWvnQ10hl1X8_bgev-blyd3aMRZKp3ZAnbMVh577VWfE1Ltv31TPPvUeIA1XID6obzG6dSq_dtGGZvLkwQpFrRTfryB24nX03etBIUmDrWuO3Yj3OOLiLBD93xT1f-9p03R1rVFyWh8Bs4wAKv-B3z9ZS4fhCVw67RfKvffB8Uvj1U0fGDdqeUZCERzledwCfmyIJbOFuGdmdAaNeF70sq4bbbAgTraHwmDKGuOVCg7V3zSdMcxsCueemAMY6qIJjnX1kNpZuETd3r5KIrJLj4kMw-Frovrbei1oZgd4l0nGsNLV7X3vdkDXXQdcsMZZ3LWFS9IRvO9VdMWp9uNnu0sGZXTcMA1akcneheG86NtpPbgQR32LdisJt8qvm6vxEwCfn1yAOiimwlk9CizYndJWx1zFe8j-t4UbWIv8HxrvT8e9KJyQTFxOs1QflGCDbgV-lBESOo4CUlpSFPir77PuSqCmpaXuqYeyeAD0QbopMh6_yaF882ZDa59AAexSpOpe-V90V4cCGQk9WNe9nU61Yf5NuJNKHfk-xNfJYcxf6iFwqlKaXaUrP4iAFX1noYLkgYKxDCT-RM8JOHQIOh-EHbREbG9hq3dssPmdgAVIaAsptMTziXjkRktgzPTrR06T14u7wFGHyp46KJS9V9A7vcKVr8JL-CtVAfkw3E9J8lLzUIisPcfgEfGjNcyUkF9dYrqo81DBeOysBduw1sZrfows4ZWi2jgQ4k9irfiV0OW4AmbipihVJgWa-ic_9A6aQ0lb9ETk55RA94Hi2muJT-G_seA_Kwhnth0BDOX14vWGocCuAbgvimJEZHi_SkU7BrkRA9D8OeCzkoPC3Fbm1KUYGQW4qlVu2IrHlAkalsY5fWQigXCmjZi3_Pr8MW-GPvKME78GolPhwQzw-C6bWrjEYTg-5fZ7VL1-dL_3h6E0bajppRvmYNdVj3oIhoNGxqp9Z8t_Njz0jlGhacOEH3HU2JxY0C9eAGYRNXBmORFvCUTHZu1gQSflsDDwrmQj19vubEEcRO418zzY-_1oOumKQ_AMLvxBBYi6fQ9HZBKs8QuAoGxztMGKmA-yAZv_BCLv-gDYnSv0ISNVmuQj4cxWZ5cvKRVCtOXB5cmE161ip-TO7yINddWhULNij62BWn-G-m8AH4EVBbN3_MXp2XUH6gq9qaNwuFGLz_ngwh8e92YknuihlYtu6L7cLFDRn51PCr7LKTr9eCtjtM1aNfvke0VIn5rei8Auyu_7nuGt_qPnm32cUFhrgQ1Hsu25xM2l_3VcYLvkw6viNsy29pmVkUWnHpA-uWo0YQvoxNPvb5Yf-SYX8a1IPJurwZGGZTA==
"""

def get_user_credentials():
    """
    Decrypt and return the embedded user credentials.
    
    Returns:
        dict: The decrypted user credentials
    """
    try:
        # Create cipher with embedded key
        cipher = Fernet(_EMBEDDED_KEY)
        
        # Decode the base64 encoded string
        encoded_data = _ENCODED_USER_CREDENTIALS.strip()
        
        # Decrypt the data
        decrypted_data = cipher.decrypt(encoded_data.encode())
        
        # Parse JSON
        credentials_dict = json.loads(decrypted_data.decode())
        
        return credentials_dict
    except Exception as e:
        print(f"Error decrypting embedded credentials: {str(e)}")
        return None

def embed_credentials_from_file(file_path="user_firebase_config.json", output_file=None):
    """
    Utility function to encrypt credentials from a file and print the encrypted string
    to be embedded in this file.
    
    Args:
        file_path (str): Path to the credentials file
        output_file (str, optional): If provided, updates this file with the encrypted credentials
    
    This function is meant to be run by developers, not end users.
    """
    try:
        # Generate a new key if needed
        key = Fernet.generate_key()
        print(f"Generated encryption key: {key.decode()}")
        print("Replace _EMBEDDED_KEY in the code with this value.")
        
        # Create cipher
        cipher = Fernet(key)
        
        # Read credentials file
        with open(file_path, "r") as f:
            credentials = json.load(f)
        
        # Convert to JSON string
        json_data = json.dumps(credentials)
        
        # Encrypt the data
        encrypted_data = cipher.encrypt(json_data.encode())
        
        # Convert to string for embedding in code
        encoded_str = encrypted_data.decode()
        
        print("\nEncrypted credentials:")
        print(encoded_str)
        print("\nReplace _ENCODED_USER_CREDENTIALS in the code with this value.")
        
        # Optionally update this file
        if output_file:
            with open(output_file, "r") as f:
                content = f.read()
            
            # Replace the placeholder with the encrypted credentials
            content = content.replace("gAAAAABoEB5dm1C-7S2-U_eh2GFyjJKUDOX6V90NoiESyLeqsX93-LZMCITnDttEEuS12zm0fDG21LlQUDc4Q7E-BfAy09igRFOtcfD316ZCOEqx8tdTmkbgb72mmRQ7NC6p-CUdDdPlIIkqVCAnb817cGL_Telf_CWpQm6j6ZlznouHywudOjfj-eUQUhaqw8h4frBtGWG1JIoL2XnXCFv0EQn6JCoIikGOBNOH2ArrMUm7gMLIMvNRw_tvdg-6_brQtjS-ybjrfvr2ndjGV3duPJVmGRW_Yxgq8cIvNWby7g2MyGu7jmf_xuXGztXb0o_JfLT1PxSFSELrKYxNB7UZgvY6a4Eui_3eGzQy9Auml_1p4t--TXXIw7EVbStsXpl2EgM9p3-4_bKMz3AlxaQ8vpz0N4Hyj0tpVei-dVcFKkN0dyHeSNUJjmK1PiuBYoAP_ODOM3rpEqXsYOfhLgI-TFtnxMPrCiXAyWEyecktQEkrXkUaY_YrMgCiyh8JLirb0b3L1dasdLfI0d0vKQI-VitmcoDna4Bvg8RKB66eXtR1yAxLdqw8IIoEEI_b6Gn74qT4WFC1CUdWVGo-1J4catU-SfCiGLQyEQg-AzO-UnctaVvWl2BEoIsp8G349FRKExxng0XoMh6SjS4JrYpQ79FS5jSgDiqC7ru9vUHCw_lTPACC_Oq9EUL7SELI7lvSP6FzG_GbGtjrUWSpQzDpa1YQe7_FFol4PIQUZ3mLiBPEJVNz81RLnd4ynMWFJRPTlTzftrpFyUr-f3CYulAxzO7NPe8z0WFNp5RiKeDLeuxFH79JEdk09AiIdM8z-a2gdnjLeqXCjSbwGneA348QQfdnAcH0-z5Ys2K4MqZPTq4Vivm7vz-FEcVGuMgsQmXj-O9HcECZliEF_uNJ_ZhG6wqTDbRnH4vj8I2JLfOa6SnVNgNx0elcNCpVGzHYHve_pMqwy3Cm2uVJA0f3Kt0px0MIsK8263sN-33cTWXcJh3HzKS08-17bbtXkurkNdw4irXpxRpX_grKDkiC-Z-zb0geiOItQ2chKMIG6oNjAFI3KjDwiOXlmqm8v6SdIh8Gu0MUj1lYskF_T6_TAj0mC_u5C0US3wqZRv_DaUG1dDYIEPOok8Q6MYAIvTgen7ZndWtxAvKcI4gvMpciIBUv6fQ9iUHqmqkNEoiUpdezn70KZ675-9VD6EkIXcyfo7o3-hjQ4FhtC5dvw5qG9yovMEAxkTSzlXmqqb3dgg0lQKotiqzfKysB-RVgNdwq64TkgnjBu1fjRhMUADJr0joYs8OVH-hulFJjL65mPkmKXoqCBzG7oZmgtaeFbvS9PBUmQs0Gj6R4_NYew6JmxgbllMQukkUGnsm3N3pBhTCZlhHoG2zuBUUvnKOU6rwchFH7Y7xuO-aWs73WNsL54I1PNeAUMMF6rbwVeucM8hFwXawelWw7-B8UgFtQhB38FguF8OlP7oTJVDh5giSrxixSlEHEY_cKNtoo6h6TQoevQ9k3dp_MrVP4WSz1Gy4reYJ9GximBNDSMv9Sj9VXDjyVBZQWaYboBFvX1gfJGxVuhJc-_zVUEUcI10JaFw6tZJi5N_yniXYP5LKO1mlVKS0VcEheWPVsDGAoMMBQ0Mr8NgsUClRyk53WOK-Sxy1x5TuN7AgrPBxsh-VqDFwUejYlmxgNM8cgA3oIZwq8BgoZe6AOvHjt2Hu1uoCvyelxqU5q_BMDLwzoFBkZDg3scAmKifHOGLVoOPqx4lfsELYqF7S3DGHM8TgDtO_vcwlfsM2v4cAyw284DyfwUsWL5FYaKfMG5SD6pm_4B_H6-3Um2zFdWn6ohrYpjiXvFra-fv8dvavmmWuajeRcR0UNxjZCVCxGINHvJWvtD2XriXPwAblX_R6lQb7UctqdO9X-mQZ2AsN1ORWKW5N75L1nT9ji_rKQWvnQ10hl1X8_bgev-blyd3aMRZKp3ZAnbMVh577VWfE1Ltv31TPPvUeIA1XID6obzG6dSq_dtGGZvLkwQpFrRTfryB24nX03etBIUmDrWuO3Yj3OOLiLBD93xT1f-9p03R1rVFyWh8Bs4wAKv-B3z9ZS4fhCVw67RfKvffB8Uvj1U0fGDdqeUZCERzledwCfmyIJbOFuGdmdAaNeF70sq4bbbAgTraHwmDKGuOVCg7V3zSdMcxsCueemAMY6qIJjnX1kNpZuETd3r5KIrJLj4kMw-Frovrbei1oZgd4l0nGsNLV7X3vdkDXXQdcsMZZ3LWFS9IRvO9VdMWp9uNnu0sGZXTcMA1akcneheG86NtpPbgQR32LdisJt8qvm6vxEwCfn1yAOiimwlk9CizYndJWx1zFe8j-t4UbWIv8HxrvT8e9KJyQTFxOs1QflGCDbgV-lBESOo4CUlpSFPir77PuSqCmpaXuqYeyeAD0QbopMh6_yaF882ZDa59AAexSpOpe-V90V4cCGQk9WNe9nU61Yf5NuJNKHfk-xNfJYcxf6iFwqlKaXaUrP4iAFX1noYLkgYKxDCT-RM8JOHQIOh-EHbREbG9hq3dssPmdgAVIaAsptMTziXjkRktgzPTrR06T14u7wFGHyp46KJS9V9A7vcKVr8JL-CtVAfkw3E9J8lLzUIisPcfgEfGjNcyUkF9dYrqo81DBeOysBduw1sZrfows4ZWi2jgQ4k9irfiV0OW4AmbipihVJgWa-ic_9A6aQ0lb9ETk55RA94Hi2muJT-G_seA_Kwhnth0BDOX14vWGocCuAbgvimJEZHi_SkU7BrkRA9D8OeCzkoPC3Fbm1KUYGQW4qlVu2IrHlAkalsY5fWQigXCmjZi3_Pr8MW-GPvKME78GolPhwQzw-C6bWrjEYTg-5fZ7VL1-dL_3h6E0bajppRvmYNdVj3oIhoNGxqp9Z8t_Njz0jlGhacOEH3HU2JxY0C9eAGYRNXBmORFvCUTHZu1gQSflsDDwrmQj19vubEEcRO418zzY-_1oOumKQ_AMLvxBBYi6fQ9HZBKs8QuAoGxztMGKmA-yAZv_BCLv-gDYnSv0ISNVmuQj4cxWZ5cvKRVCtOXB5cmE161ip-TO7yINddWhULNij62BWn-G-m8AH4EVBbN3_MXp2XUH6gq9qaNwuFGLz_ngwh8e92YknuihlYtu6L7cLFDRn51PCr7LKTr9eCtjtM1aNfvke0VIn5rei8Auyu_7nuGt_qPnm32cUFhrgQ1Hsu25xM2l_3VcYLvkw6viNsy29pmVkUWnHpA-uWo0YQvoxNPvb5Yf-SYX8a1IPJurwZGGZTA==", encoded_str)
            
            # Replace the key placeholder
            content = content.replace("5chNjgPX3F5uEZoXM6qsz9ruBmfcnhF5e3unN3suLxI=", key.decode())
            
            with open(output_file, "w") as f:
                f.write(content)
            
            print(f"\nUpdated {output_file} with encrypted credentials.")
        
        return True
    except Exception as e:
        print(f"Error embedding credentials: {str(e)}")
        return False

if __name__ == "__main__":
    # This code runs when the module is executed directly
    # It's used to generate the encrypted credentials to embed in the code
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "user_firebase_config.json"
    
    print(f"Embedding credentials from {file_path}...")
    embed_credentials_from_file(file_path, __file__)
