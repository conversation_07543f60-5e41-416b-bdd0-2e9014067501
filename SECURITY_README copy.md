# نظام الأمان للتطبيق (Application Security System)

## نظرة عامة (Overview)

تم تحسين نظام الأمان في التطبيق لتوفير مستويين من الوصول:

1. **المستخدم العادي (Regular User)**: يمكنه فقط التحقق من صلاحية مفتاح الترخيص وتفعيله.
2. **المسؤول (Admin)**: يمكنه الوصول إلى جميع الوظائف بما في ذلك إنشاء مفاتيح جديدة وإدارة التراخيص.

## نظام الاعتماد المشفر الجديد (New Encrypted Credentials System)

تم تحديث التطبيق ليستخدم نظام اعتماد مشفر أكثر أمانًا:

### للمسؤولين (For Administrators):

1. **firebase_credentials.enc**: ملف مشفر يحتوي على بيانات الاعتماد الكاملة.
2. **encryption.key**: مفتاح التشفير المستخدم لتشفير وفك تشفير بيانات الاعتماد.

### للمستخدمين العاديين (For Regular Users):

1. **بيانات اعتماد مدمجة في الكود**: بيانات الاعتماد مشفرة ومدمجة مباشرة في الكود، بدون ملفات خارجية مرئية للمستخدم.

## كيفية الترقية إلى النظام الجديد (How to Upgrade to the New System)

لترقية نظام الاعتماد الخاص بك إلى النظام المشفر الجديد:

### للمسؤولين (For Administrators):

1. قم بتشغيل سكريبت الترقية `migrate_credentials.py`:

   ```
   python migrate_credentials.py --service-account=serviceAccountKey.json --create-user-config --delete-original
   ```

2. سيقوم هذا السكريبت بما يلي:
   - تشفير بيانات الاعتماد من ملف `serviceAccountKey.json` وحفظها في `firebase_credentials.enc`
   - إنشاء ملف `user_firebase_config.json` مع صلاحيات محدودة
   - حذف ملف `serviceAccountKey.json` الأصلي (اختياري)

### للمستخدمين العاديين (For Regular Users):

1. قم بتشغيل سكريبت دمج بيانات الاعتماد `embed_credentials.py`:

   ```
   python embed_credentials.py --credentials-file=user_firebase_config.json --delete-original
   ```

2. سيقوم هذا السكريبت بما يلي:
   - تشفير بيانات الاعتماد من ملف `user_firebase_config.json` ودمجها في الكود
   - حذف ملف `user_firebase_config.json` الأصلي (اختياري)
   - لن يحتاج المستخدمون إلى أي ملفات اعتماد خارجية

## كيفية التوزيع (Distribution Method)

عند توزيع التطبيق:

### للمستخدمين العاديين:

1. قم بتوزيع التطبيق المجمع (compiled) مع بيانات الاعتماد المدمجة في الكود.
2. لا حاجة لتضمين أي ملفات اعتماد خارجية.

### للمسؤولين:

1. قم بتضمين ملفات `firebase_credentials.enc` و `encryption.key` مع التطبيق.
2. تأكد من أن المستخدم لديه مفتاح ترخيص بدور "admin".

## آلية العمل (How It Works)

1. يبدأ التطبيق بالبحث عن بيانات الاعتماد بالترتيب التالي:

   - متغيرات البيئة
   - ملف `firebase_config.json`
   - بيانات الاعتماد المدمجة في الكود (للمستخدمين العاديين)
   - الملف المشفر `firebase_credentials.enc` (للمسؤولين)
   - ملف `user_firebase_config.json` (طريقة قديمة)
   - كملاذ أخير، ملف `serviceAccountKey.json` (غير موصى به)

2. عندما يحاول مستخدم بدور "admin" الوصول إلى لوحة الإدارة، يقوم التطبيق بإعادة تهيئة Firebase باستخدام بيانات الاعتماد الكاملة.

3. عند إغلاق لوحة الإدارة، يعود التطبيق إلى استخدام بيانات اعتماد المستخدم العادي.

## تحسينات الأمان (Security Improvements)

هذا النظام الجديد يحسن الأمان بعدة طرق:

1. بيانات الاعتماد الحساسة مشفرة ولا يمكن الوصول إليها بسهولة.
2. لا يتم تخزين المفاتيح الخاصة في ملفات نصية عادية.
3. للمستخدمين العاديين، يتم دمج بيانات الاعتماد في الكود بدون ملفات خارجية مرئية.
4. يمكن توزيع التطبيق بأمان دون القلق من تسرب بيانات الاعتماد الكاملة.
5. يتم التحقق من دور المستخدم قبل السماح بالوصول إلى وظائف المسؤول.

## ملاحظات هامة (Important Notes)

- احتفظ بملف `encryption.key` آمنًا، فهو المفتاح لفك تشفير بيانات الاعتماد.
- تأكد من إنشاء مفاتيح ترخيص بدور "admin" فقط للأشخاص الموثوق بهم.
- يمكنك تغيير صلاحيات الوصول في Firebase لتقييد ما يمكن للمستخدمين العاديين الوصول إليه.
- عند تجميع التطبيق (compiling) باستخدام PyInstaller، سيتم تضمين بيانات الاعتماد المدمجة في الكود تلقائيًا، مما يجعلها غير مرئية للمستخدمين.
- قم بتحديث الكود المصدري بانتظام لتضمين أي تغييرات في بيانات الاعتماد.
