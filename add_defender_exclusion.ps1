# Add Windows Defender Exclusion for Grand RP Helper
# This script adds the Grand RP Helper application to Windows Defender exclusions
# Run this script with administrator privileges

# Get the current directory where the script is located
$currentDir = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition
$exePath = Join-Path -Path $currentDir -ChildPath "Grand_RP_Helper.exe"

# Check if the application exists
if (-not (Test-Path $exePath)) {
    Write-Host "Error: Grand_RP_Helper.exe not found in the current directory." -ForegroundColor Red
    Write-Host "Make sure this script is in the same directory as the application." -ForegroundColor Red
    exit 1
}

# Check if running with administrator privileges
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script requires administrator privileges." -ForegroundColor Yellow
    Write-Host "Please right-click on the script and select 'Run as administrator'." -ForegroundColor Yellow
    exit 1
}

try {
    # Add the application to Windows Defender exclusions
    Add-MpPreference -ExclusionPath $exePath -ErrorAction Stop
    
    # Add the entire directory to exclusions to protect config files
    Add-MpPreference -ExclusionPath $currentDir -ErrorAction Stop
    
    Write-Host "Successfully added Grand RP Helper to Windows Defender exclusions!" -ForegroundColor Green
    Write-Host "The application should no longer be removed during Windows updates." -ForegroundColor Green
} catch {
    Write-Host "Error adding exclusion: $_" -ForegroundColor Red
    Write-Host "Please try adding the exclusion manually through Windows Security settings." -ForegroundColor Yellow
}

# Pause to let the user read the message
Write-Host "`nPress any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
