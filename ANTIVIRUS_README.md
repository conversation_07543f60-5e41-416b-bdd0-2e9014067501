# Reducing Antivirus False Positives

This guide explains why your application might be triggering false positives in antivirus software and provides solutions to address this issue.

## Why False Positives Occur

Your application is being flagged by antivirus software for several legitimate reasons:

1. **PyInstaller Packaging**: PyInstaller-packaged applications are often flagged because malware authors also use PyInstaller.

2. **System Interaction**: Your application uses libraries that interact with the system at a low level:
   - `pyautogui` for simulating keyboard and mouse inputs
   - `pynput` for monitoring keyboard and mouse events
   - `keyboard` for global hotkeys
   - `pygetwindow` for window manipulation
   - Windows API calls through various libraries

3. **Network Connections**: Your application connects to Firebase, which can trigger heuristic detection.

4. **License Verification**: The license verification system uses techniques that are similar to those used by malware.

## Solutions

### 1. Code Signing (Most Effective)

Code signing your application with a trusted certificate is the most effective solution. See `code_signing_guide.md` for detailed instructions.

### 2. Optimized Building

Use the provided build script and spec file for optimized building:

```bash
python build.py
```

For signing:
```bash
python build.py --sign --cert YourCertificate.pfx --password YourPassword
```

### 3. Submit False Positive Reports

Submit false positive reports to antivirus vendors:

1. **Windows Defender**: https://www.microsoft.com/en-us/wdsi/filesubmission
2. **Avast/AVG**: https://www.avast.com/false-positive-file-form.php
3. **Kaspersky**: https://www.kaspersky.com/downloads/free-virus-removal-tool
4. **McAfee**: https://www.mcafee.com/enterprise/en-us/threat-center/resources/false-positive.html
5. **Symantec/Norton**: https://submit.symantec.com/false_positive/

Include:
- Your application executable
- A description of what your application does
- Your contact information

### 4. Whitelist Your Application

Instruct your users to whitelist your application in their antivirus software:

1. **Windows Defender**:
   - Go to Windows Security > Virus & threat protection
   - Under "Virus & threat protection settings," click "Manage settings"
   - Scroll down to "Exclusions" and click "Add or remove exclusions"
   - Click "Add an exclusion" and select your application

2. **Other Antivirus Software**:
   - Most antivirus software has similar options to add exclusions or trusted applications

### 5. Modify Your Code

Consider these code modifications:

1. **Reduce Suspicious Behaviors**:
   - Avoid writing to system directories
   - Don't modify system settings without clear user consent
   - Use less aggressive methods for license verification

2. **Use More Trusted Libraries**:
   - Consider alternatives to direct Windows API calls where possible
   - Use higher-level libraries when available

3. **Add Clear Documentation**:
   - Include detailed comments in your code
   - Add a README file explaining what your application does

## Building with Reduced False Positives

1. Update the company and product information in `file_version_info.txt`
2. Run the build script: `python build.py`
3. If you have a code signing certificate, use: `python build.py --sign --cert YourCertificate.pfx --password YourPassword`
4. Test the resulting executable with VirusTotal
5. Submit false positive reports for any remaining detections
