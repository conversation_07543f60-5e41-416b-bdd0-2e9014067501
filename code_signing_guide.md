# Code Signing Guide for Windows Applications

Code signing is the most effective way to prevent false positives in antivirus software. When your application is signed with a trusted certificate, it establishes trust with Windows and antivirus software.

## Steps to Sign Your Application

1. **Purchase a Code Signing Certificate**:
   - Purchase from a trusted Certificate Authority (CA) like:
     - DigiCert
     - Sectigo (formerly Comodo)
     - GlobalSign
     - Thawte
   - Costs typically range from $100-$500 per year

2. **Install the Certificate**:
   - Follow the CA's instructions to install the certificate on your development machine
   - You'll typically receive a PFX file with your certificate

3. **Sign Your Executable**:
   - Use SignTool (included with Windows SDK) to sign your executable:

```powershell
signtool sign /f YourCertificate.pfx /p YourPassword /tr http://timestamp.digicert.com /td sha256 /fd sha256 "YourApplication.exe"
```

4. **Verify the Signature**:
```powershell
signtool verify /pa "YourApplication.exe"
```

## Alternative: Self-Signing (Less Effective)

If you can't purchase a commercial certificate, you can create a self-signed certificate, but this won't be as effective at preventing false positives:

```powershell
# Create a self-signed certificate
New-SelfSignedCertificate -Subject "CN=YourName" -Type CodeSigning -CertStoreLocation cert:\CurrentUser\My

# Export the certificate to a PFX file
$password = ConvertTo-SecureString -String "YourPassword" -Force -AsPlainText
Export-PfxCertificate -Cert cert:\CurrentUser\My\CertificateThumbprint -FilePath YourCertificate.pfx -Password $password

# Sign your application
signtool sign /f YourCertificate.pfx /p YourPassword "YourApplication.exe"
```

Note: Self-signed certificates will still trigger warnings in Windows and may not significantly reduce antivirus false positives.
