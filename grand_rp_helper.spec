# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('Logo.ico', '.'),
        ('config.json', '.'),
        ('.env', '.'),
        ('license.json', '.'),
        ('macros.json', '.'),
        ('macro_usage.json', '.'),
    ],
    hiddenimports=[
        'ttkbootstrap',
        'pyautogui',
        'pynput',
        'keyboard',
        'pygetwindow',
        'firebase_admin',
        'win32event',
        'win32api',
        'winerror',
        'ctypes',
        'pickle',
        'hashlib',
        'wmi',
        'firebase_admin.db',
        'requests',
        'urllib3',
        'charset_normalizer',
        'idna',
        'certifi',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Grand_RP_Helper',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='Logo.ico',
    version='file_version_info.txt',
    uac_admin=False,
)
