"""
Build script for Grand RP Helper application.
This script builds the application using PyInstaller with optimized settings
to reduce false positives in antivirus software.
"""

import os
import sys
import shutil
import subprocess
import argparse

def clean_build_directories():
    """Clean build and dist directories."""
    print("Cleaning build directories...")
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    print("Build directories cleaned.")

def build_application(upx=True, sign=False, cert_file=None, cert_password=None):
    """Build the application using PyInstaller."""
    print("Building application...")

    # Base command
    cmd = ["pyinstaller", "grand_rp_helper.spec"]

    # Add UPX compression if requested
    if not upx:
        cmd.append("--noupx")

    # Run PyInstaller
    subprocess.run(cmd, check=True)

    print("Application built successfully.")

    # Copy protection scripts to dist directory
    copy_protection_files()

    # Sign the executable if requested
    if sign and cert_file and cert_password:
        sign_executable(cert_file, cert_password)

def copy_protection_files():
    """Copy Windows protection scripts to the dist directory."""
    print("Adding Windows protection files...")

    # Protection script files
    protection_files = [
        "add_defender_exclusion.ps1",
        "Add_Windows_Protection.bat",
        "WINDOWS_PROTECTION_README.md",
        "install.bat"
    ]

    # Create the files if they don't exist
    if not os.path.exists("add_defender_exclusion.ps1"):
        create_defender_exclusion_script()

    if not os.path.exists("Add_Windows_Protection.bat"):
        create_protection_batch_file()

    if not os.path.exists("WINDOWS_PROTECTION_README.md"):
        create_protection_readme()

    if not os.path.exists("install.bat"):
        create_installer_script()

    # Copy files to dist directory
    for file in protection_files:
        if os.path.exists(file):
            shutil.copy(file, os.path.join("dist", file))
            print(f"Copied {file} to dist directory")
        else:
            print(f"Warning: {file} not found, skipping")

    print("Windows protection files added successfully.")

def create_defender_exclusion_script():
    """Create the PowerShell script to add Windows Defender exclusions."""
    script_content = """# Add Windows Defender Exclusion for Grand RP Helper
# This script adds the Grand RP Helper application to Windows Defender exclusions
# Run this script with administrator privileges

# Get the current directory where the script is located
$currentDir = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition
$exePath = Join-Path -Path $currentDir -ChildPath "Grand_RP_Helper.exe"

# Check if the application exists
if (-not (Test-Path $exePath)) {
    Write-Host "Error: Grand_RP_Helper.exe not found in the current directory." -ForegroundColor Red
    Write-Host "Make sure this script is in the same directory as the application." -ForegroundColor Red
    exit 1
}

# Check if running with administrator privileges
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script requires administrator privileges." -ForegroundColor Yellow
    Write-Host "Please right-click on the script and select 'Run as administrator'." -ForegroundColor Yellow
    exit 1
}

try {
    # Add the application to Windows Defender exclusions
    Add-MpPreference -ExclusionPath $exePath -ErrorAction Stop

    # Add the entire directory to exclusions to protect config files
    Add-MpPreference -ExclusionPath $currentDir -ErrorAction Stop

    Write-Host "Successfully added Grand RP Helper to Windows Defender exclusions!" -ForegroundColor Green
    Write-Host "The application should no longer be removed during Windows updates." -ForegroundColor Green
} catch {
    Write-Host "Error adding exclusion: $_" -ForegroundColor Red
    Write-Host "Please try adding the exclusion manually through Windows Security settings." -ForegroundColor Yellow
}

# Pause to let the user read the message
Write-Host "`nPress any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
"""

    with open("add_defender_exclusion.ps1", "w") as f:
        f.write(script_content)

    print("Created add_defender_exclusion.ps1")

def create_protection_batch_file():
    """Create the batch file to run the PowerShell script with admin privileges."""
    batch_content = """@echo off
echo Adding Grand RP Helper to Windows Defender exclusions...
echo This will help prevent Windows from removing the application during updates.
echo.
echo Please allow the administrator prompt when it appears.
echo.
powershell -ExecutionPolicy Bypass -Command "Start-Process powershell -ArgumentList '-ExecutionPolicy Bypass -File \\"%~dp0add_defender_exclusion.ps1\\"' -Verb RunAs"
echo.
echo If you didn't see a UAC prompt, please right-click on this file and select "Run as administrator"
echo.
pause
"""

    with open("Add_Windows_Protection.bat", "w") as f:
        f.write(batch_content)

    print("Created Add_Windows_Protection.bat")

def create_protection_readme():
    """Create the README file with instructions for Windows protection."""
    readme_content = """# Preventing Windows from Removing Grand RP Helper

This guide explains how to prevent Windows from removing the Grand RP Helper application during Windows updates.

## Why Windows Removes the Application

Windows Defender and other antivirus software may flag the Grand RP Helper application as potentially unwanted because:

1. It uses automation features to control keyboard and mouse
2. It monitors keyboard input for hotkeys
3. It interacts with other applications
4. It connects to online services

These are all legitimate features of the application, but they can trigger false positives in security software.

## Solution 1: Run the Protection Script (Recommended)

The easiest way to prevent Windows from removing the application is to run the included protection script:

1. Locate the file `Add_Windows_Protection.bat` in the application folder
2. Right-click on it and select **Run as administrator**
3. Follow the on-screen instructions
4. When prompted by User Account Control (UAC), click **Yes**

This script will add the application to Windows Defender exclusions, which should prevent it from being removed during updates.

## Solution 2: Manually Add Exclusion to Windows Defender

If the script doesn't work, you can manually add an exclusion:

1. Open **Windows Security** (search for it in the Start menu)
2. Click on **Virus & threat protection**
3. Under "Virus & threat protection settings," click **Manage settings**
4. Scroll down to **Exclusions** and click **Add or remove exclusions**
5. Click **Add an exclusion** and select **File**
6. Browse to the location of `Grand_RP_Helper.exe` and select it
7. Click **Add an exclusion** again and select **Folder**
8. Browse to the folder containing the application and select it

## Solution 3: Disable Real-time Protection Temporarily

If you're having trouble with the above methods, you can temporarily disable real-time protection while installing:

1. Open **Windows Security**
2. Click on **Virus & threat protection**
3. Under "Virus & threat protection settings," click **Manage settings**
4. Turn off **Real-time protection**
5. Install and run the application
6. Turn real-time protection back on
7. Add exclusions as described in Solution 2

**Note:** This is not recommended as a permanent solution, as it leaves your system vulnerable to actual threats.

## Solution 4: Use a Different Antivirus

If Windows Defender continues to remove the application, you might consider using a different antivirus solution that allows more granular control over exclusions.

## Contact Support

If you continue to experience issues with Windows removing the application, please contact support for further assistance.

## Technical Details

The protection script adds the following items to Windows Defender exclusions:
- The Grand_RP_Helper.exe executable file
- The folder containing the application and its configuration files

This ensures that Windows Defender won't flag or remove any part of the application during scans or updates.
"""

    with open("WINDOWS_PROTECTION_README.md", "w") as f:
        f.write(readme_content)

    print("Created WINDOWS_PROTECTION_README.md")

def create_installer_script():
    """Create the installer batch script."""
    installer_content = """@echo off
echo ===================================================
echo Grand RP Helper - Installation and Protection Setup
echo ===================================================
echo.
echo This script will help you install and protect Grand RP Helper
echo from being removed by Windows updates or antivirus software.
echo.
echo What would you like to do?
echo.
echo 1. Install and protect Grand RP Helper (Recommended)
echo 2. Only add Windows Defender exclusions
echo 3. Exit
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    goto install_and_protect
) else if "%choice%"=="2" (
    goto add_exclusions
) else if "%choice%"=="3" (
    goto end
) else (
    echo Invalid choice. Please try again.
    pause
    exit /b
)

:install_and_protect
echo.
echo Installing Grand RP Helper...
echo.

:: Create a directory in Program Files
set "install_dir=%ProgramFiles%\\Grand RP Helper"
if not exist "%install_dir%" mkdir "%install_dir%"

:: Copy all files to the installation directory
echo Copying files to %install_dir%...
xcopy /E /I /Y "." "%install_dir%"

:: Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut([Environment]::GetFolderPath('Desktop') + '\\Grand RP Helper.lnk'); $Shortcut.TargetPath = '%install_dir%\\Grand_RP_Helper.exe'; $Shortcut.Save()"

:: Add Windows Defender exclusions
echo.
echo Adding Windows Defender exclusions...
cd /d "%install_dir%"
call Add_Windows_Protection.bat

echo.
echo Installation complete!
echo Grand RP Helper has been installed to: %install_dir%
echo A shortcut has been created on your desktop.
echo.
echo You can now run Grand RP Helper from your desktop.
goto end

:add_exclusions
echo.
echo Adding Windows Defender exclusions...
call Add_Windows_Protection.bat
goto end

:end
echo.
echo Thank you for using Grand RP Helper!
echo.
pause
"""

    with open("install.bat", "w") as f:
        f.write(installer_content)

    print("Created install.bat")

def sign_executable(cert_file, cert_password):
    """Sign the executable with the provided certificate."""
    print("Signing executable...")

    exe_path = os.path.join("dist", "Grand_RP_Helper.exe")

    # Check if signtool is available
    signtool_cmd = "signtool.exe"
    try:
        subprocess.run([signtool_cmd, "/?"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except FileNotFoundError:
        print("Error: signtool.exe not found. Make sure Windows SDK is installed and signtool is in your PATH.")
        return

    # Sign the executable
    cmd = [
        signtool_cmd, "sign",
        "/f", cert_file,
        "/p", cert_password,
        "/tr", "http://timestamp.digicert.com",
        "/td", "sha256",
        "/fd", "sha256",
        exe_path
    ]

    try:
        subprocess.run(cmd, check=True)
        print("Executable signed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error signing executable: {e}")

def main():
    parser = argparse.ArgumentParser(description="Build Grand RP Helper application")
    parser.add_argument("--noupx", action="store_true", help="Disable UPX compression")
    parser.add_argument("--sign", action="store_true", help="Sign the executable")
    parser.add_argument("--cert", help="Path to the certificate file (.pfx)")
    parser.add_argument("--password", help="Certificate password")

    args = parser.parse_args()

    # Validate signing arguments
    if args.sign and (not args.cert or not args.password):
        parser.error("--sign requires --cert and --password")

    # Clean build directories
    clean_build_directories()

    # Build the application
    build_application(
        upx=not args.noupx,
        sign=args.sign,
        cert_file=args.cert,
        cert_password=args.password
    )

    print("Build process completed.")

if __name__ == "__main__":
    main()
