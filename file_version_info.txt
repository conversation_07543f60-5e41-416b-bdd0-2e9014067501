VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [StringStruct(u'CompanyName', u'Your Company Name'),
          StringStruct(u'FileDescription', u'Grand RP Helper - Automation Tool'),
          StringStruct(u'FileVersion', u'1.0.0'),
          StringStruct(u'InternalName', u'Grand_RP_Helper'),
          StringStruct(u'LegalCopyright', u'Copyright (c) 2023 Your Company Name'),
          StringStruct(u'OriginalFilename', u'Grand_RP_Helper.exe'),
          StringStruct(u'ProductName', u'Grand RP Helper'),
          StringStruct(u'ProductVersion', u'1.0.0')]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
