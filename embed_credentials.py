#!/usr/bin/env python
"""
Utility script to embed Firebase credentials into the code.
This script takes the user_firebase_config.json file and embeds it into the embedded_credentials.py file.
"""

import os
import sys
import argparse
from embedded_credentials import embed_credentials_from_file

def main():
    parser = argparse.ArgumentParser(
        description="Embed Firebase credentials into the code to hide them from users"
    )
    
    parser.add_argument(
        "--credentials-file", 
        default="user_firebase_config.json",
        help="Path to the credentials JSON file (default: user_firebase_config.json)"
    )
    
    parser.add_argument(
        "--output-file", 
        default="embedded_credentials.py",
        help="Path to the output Python file (default: embedded_credentials.py)"
    )
    
    parser.add_argument(
        "--delete-original", 
        action="store_true",
        help="Delete the original credentials file after embedding"
    )
    
    args = parser.parse_args()
    
    # Check if credentials file exists
    if not os.path.exists(args.credentials_file):
        print(f"Error: Credentials file '{args.credentials_file}' not found.")
        return 1
    
    # Check if output file exists
    if not os.path.exists(args.output_file):
        print(f"Error: Output file '{args.output_file}' not found.")
        return 1
    
    # Embed the credentials
    print(f"Embedding credentials from {args.credentials_file} into {args.output_file}...")
    success = embed_credentials_from_file(
        file_path=args.credentials_file,
        output_file=args.output_file
    )
    
    if not success:
        print("Failed to embed credentials.")
        return 1
    
    print("Credentials successfully embedded into the code.")
    
    # Delete the original file if requested
    if args.delete_original:
        try:
            os.remove(args.credentials_file)
            print(f"Deleted original credentials file: {args.credentials_file}")
        except Exception as e:
            print(f"Warning: Could not delete original file: {str(e)}")
    
    print("\nNext steps:")
    print("1. Compile your application to further hide the embedded credentials")
    print("2. Distribute the application without any external credential files")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
