"""
Module for encrypting and decrypting sensitive configuration data.
This approach secures sensitive information like email credentials.
"""

import base64
import json
import os
from cryptography.fernet import Fernet

# Encryption key embedded in code (this is just for basic obfuscation)
# In a real-world scenario, you might want to use a more sophisticated approach
_CONFIG_KEY = b'5chNjgPX3F5uEZoXM6qsz9ruBmfcnhF5e3unN3suLxI='  # Same key as embedded_credentials for simplicity

def encrypt_data(data_dict):
    """
    Encrypt a dictionary of data.
    
    Args:
        data_dict (dict): The dictionary to encrypt
        
    Returns:
        str: Base64 encoded encrypted string
    """
    try:
        # Create cipher with embedded key
        cipher = Fernet(_CONFIG_KEY)
        
        # Convert to JSON string
        json_data = json.dumps(data_dict)
        
        # Encrypt the data
        encrypted_data = cipher.encrypt(json_data.encode())
        
        # Convert to base64 string for storage
        return base64.b64encode(encrypted_data).decode()
    except Exception as e:
        print(f"Error encrypting data: {str(e)}")
        return None

def decrypt_data(encrypted_str):
    """
    Decrypt an encrypted string back to a dictionary.
    
    Args:
        encrypted_str (str): Base64 encoded encrypted string
        
    Returns:
        dict: The decrypted dictionary
    """
    try:
        # Create cipher with embedded key
        cipher = Fernet(_CONFIG_KEY)
        
        # Decode the base64 string
        encrypted_data = base64.b64decode(encrypted_str)
        
        # Decrypt the data
        decrypted_data = cipher.decrypt(encrypted_data)
        
        # Parse JSON
        data_dict = json.loads(decrypted_data.decode())
        
        return data_dict
    except Exception as e:
        print(f"Error decrypting data: {str(e)}")
        return None

def encrypt_config_section(config_dict, section_name, sensitive_data):
    """
    Encrypt a section of configuration data and add it to the config dictionary.
    
    Args:
        config_dict (dict): The configuration dictionary to update
        section_name (str): The name of the section to encrypt
        sensitive_data (dict): The sensitive data to encrypt
        
    Returns:
        dict: Updated configuration dictionary with encrypted section
    """
    encrypted_data = encrypt_data(sensitive_data)
    if encrypted_data:
        config_dict[section_name] = encrypted_data
    return config_dict

def decrypt_config_section(config_dict, section_name):
    """
    Decrypt a section of configuration data from the config dictionary.
    
    Args:
        config_dict (dict): The configuration dictionary
        section_name (str): The name of the encrypted section
        
    Returns:
        dict: The decrypted section data or None if not found/error
    """
    if section_name in config_dict:
        return decrypt_data(config_dict[section_name])
    return None
